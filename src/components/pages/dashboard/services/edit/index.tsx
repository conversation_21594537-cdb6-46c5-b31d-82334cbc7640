"use client";

import React, { useState, useTransition, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import Link from "next/link";
import DashboardLayout from "@/components/layout/dashboardlayout";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { updateService } from "@/actions/entities/services";
import { EnhancedServiceSchema } from "../new/types";
import { ServiceFormValues } from "../new/types";
import CombinedServiceForm from "../new/components/CombinedServiceForm";
import { ArrowLeft, Check, Save } from "lucide-react";
import EnhancedServiceFormSummary from "../new/components/ServiceFormSummary";
import { Service } from "../types";

interface ServiceEditPageProps {
  service: Service;
}

const ServiceEditPage: React.FC<ServiceEditPageProps> = ({ service }) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  // Initialize the form with enhanced schema and service data
  const form = useForm<ServiceFormValues>({
    resolver: zodResolver(EnhancedServiceSchema),
    defaultValues: {
      serviceNumber: service.serviceNumber,
      customerName: service.customerName,
      customerPhone: service.customerPhone,
      customerEmail: service.customerEmail || "",
      deviceType: service.deviceType,
      deviceBrand: service.deviceBrand,
      deviceModel: service.deviceModel,
      deviceSerialNumber: service.deviceSerialNumber || "",
      problemDescription: service.problemDescription,
      estimatedCost: service.estimatedCost || 0,
      estimatedCompletionDate: service.estimatedCompletionDate
        ? service.estimatedCompletionDate.split("T")[0]
        : "",
      diagnosisNotes: service.diagnosisNotes || "",
      repairNotes: service.repairNotes || "",
      customerAddress: service.customerAddress || "",
      warrantyPeriod: service.warrantyPeriod || 0,
      priorityLevel: service.priorityLevel || "MEDIUM",
      customerId: service.customerId || "",
      attachments: service.lampiran || [],
      transactionDate: service.receivedDate
        ? new Date(service.receivedDate)
        : new Date(),
      dueDate: service.estimatedCompletionDate
        ? new Date(service.estimatedCompletionDate)
        : undefined,
      isDraft: service.isDraft || false,
      status: service.status,
    },
  });

  // Reset form when service data changes
  useEffect(() => {
    form.reset({
      serviceNumber: service.serviceNumber,
      customerName: service.customerName,
      customerPhone: service.customerPhone,
      customerEmail: service.customerEmail || "",
      deviceType: service.deviceType,
      deviceBrand: service.deviceBrand,
      deviceModel: service.deviceModel,
      deviceSerialNumber: service.deviceSerialNumber || "",
      problemDescription: service.problemDescription,
      estimatedCost: service.estimatedCost || 0,
      estimatedCompletionDate: service.estimatedCompletionDate
        ? service.estimatedCompletionDate.split("T")[0]
        : "",
      diagnosisNotes: service.diagnosisNotes || "",
      repairNotes: service.repairNotes || "",
      customerAddress: service.customerAddress || "",
      warrantyPeriod: service.warrantyPeriod || 0,
      priorityLevel: service.priorityLevel || "MEDIUM",
      customerId: service.customerId || "",
      attachments: service.lampiran || [],
      transactionDate: service.receivedDate
        ? new Date(service.receivedDate)
        : new Date(),
      dueDate: service.estimatedCompletionDate
        ? new Date(service.estimatedCompletionDate)
        : undefined,
      isDraft: service.isDraft || false,
      status: service.status,
    });
  }, [service, form]);

  const onSubmit = (values: ServiceFormValues) => {
    startTransition(async () => {
      try {
        // Include all the fields from the EnhancedServiceSchema
        const serviceData = {
          serviceNumber: values.serviceNumber,
          customerName: values.customerName,
          customerPhone: values.customerPhone,
          customerEmail: values.customerEmail,
          deviceType: values.deviceType,
          deviceBrand: values.deviceBrand,
          deviceModel: values.deviceModel,
          deviceSerialNumber: values.deviceSerialNumber,
          problemDescription: values.problemDescription,
          estimatedCost: values.estimatedCost,
          estimatedCompletionDate: values.estimatedCompletionDate,
          diagnosisNotes: values.diagnosisNotes,
          repairNotes: values.repairNotes,
          warrantyPeriod: values.warrantyPeriod,
          priorityLevel: values.priorityLevel,
          customerId: values.customerId,
          transactionDate: values.transactionDate || new Date(),
          dueDate: values.dueDate,
          isDraft: false,
          status: values.status,
        };

        const result = await updateService(service.id, serviceData);
        if (result.success) {
          toast.success(result.success);
          // Redirect to service detail page
          router.push(`/dashboard/services/management/${service.id}`);
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  const onSaveDraft = (values: ServiceFormValues) => {
    startTransition(async () => {
      try {
        // Include all the fields from the EnhancedServiceSchema
        const serviceData = {
          serviceNumber: values.serviceNumber,
          customerName: values.customerName,
          customerPhone: values.customerPhone,
          customerEmail: values.customerEmail,
          deviceType: values.deviceType,
          deviceBrand: values.deviceBrand,
          deviceModel: values.deviceModel,
          deviceSerialNumber: values.deviceSerialNumber,
          problemDescription: values.problemDescription,
          estimatedCost: values.estimatedCost,
          estimatedCompletionDate: values.estimatedCompletionDate,
          diagnosisNotes: values.diagnosisNotes,
          repairNotes: values.repairNotes,
          warrantyPeriod: values.warrantyPeriod,
          priorityLevel: values.priorityLevel,
          customerId: values.customerId,
          transactionDate: values.transactionDate || new Date(),
          dueDate: values.dueDate,
          isDraft: true,
          status: values.status,
        };

        const result = await updateService(service.id, serviceData);
        if (result.success) {
          toast.success("Draft servis berhasil disimpan!");
          // Redirect to services page with draft tab
          router.push("/dashboard/services/management?tab=drafts");
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  // Watch form values for summary
  const formValues = form.watch();

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Edit Servis</h1>
            <p className="text-muted-foreground">
              Edit informasi servis {service.serviceNumber}
            </p>
          </div>
          <Button variant="outline" asChild className="gap-2 cursor-pointer">
            <Link
              href={`/dashboard/services/management/${service.id}`}
              className="cursor-pointer"
            >
              <ArrowLeft className="h-4 w-4" />
              Kembali
            </Link>
          </Button>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Main Form Content */}
              <div className="lg:col-span-2">
                <CombinedServiceForm
                  control={form.control}
                  isPending={isPending}
                />
              </div>

              {/* Summary Sidebar */}
              <div className="lg:col-span-1">
                <EnhancedServiceFormSummary
                  formValues={formValues}
                  isPending={isPending}
                />

                <div className="mt-6 space-y-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={form.handleSubmit(onSaveDraft)}
                    className="w-full gap-2 cursor-pointer"
                    disabled={isPending}
                  >
                    <Save className="h-4 w-4" />
                    {isPending ? "Menyimpan..." : "Simpan ke Draft"}
                  </Button>
                  <Button
                    type="submit"
                    className="w-full gap-2 cursor-pointer"
                    disabled={isPending}
                  >
                    <Check className="h-4 w-4" />
                    {isPending ? "Menyimpan..." : "Simpan Perubahan"}
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </DashboardLayout>
  );
};

export default ServiceEditPage;
