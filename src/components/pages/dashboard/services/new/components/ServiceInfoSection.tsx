import React from "react";
import { Control } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ServiceFormValues, priorityLevelOptions } from "../types";
import { DeviceType } from "../../types";
import {
  Smartphone,
  Monitor,
  HardDrive,
  Laptop,
  DollarSign,
  Clock,
  AlertTriangle,
  FileText,
} from "lucide-react";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { id } from "date-fns/locale/id";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface ServiceInfoSectionProps {
  control: Control<ServiceFormValues>;
  isPending: boolean;
}

const ServiceInfoSection: React.FC<ServiceInfoSectionProps> = ({
  control,
  isPending,
}) => {
  return (
    <div className="space-y-8">
      {/* Device Information */}
      <div className="space-y-6">
        {/* <div className="flex items-center gap-2 mb-4">
          <Smartphone className="h-5 w-5 text-blue-600" />
          <h4 className="text-md font-medium text-gray-900">
            Informasi Perangkat
          </h4>
        </div> */}

        {/* Tipe perangkat */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={control}
            name="deviceType"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  <Monitor className="h-4 w-4 text-blue-600" />
                  Tipe Perangkat
                </FormLabel>
                <Select
                  disabled={isPending}
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih tipe perangkat" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value={DeviceType.LAPTOP}>Laptop</SelectItem>
                    <SelectItem value={DeviceType.DESKTOP}>
                      Desktop PC
                    </SelectItem>
                    <SelectItem value={DeviceType.PHONE}>Smartphone</SelectItem>
                    <SelectItem value={DeviceType.TABLET}>Tablet</SelectItem>
                    <SelectItem value={DeviceType.PRINTER}>Printer</SelectItem>
                    <SelectItem value={DeviceType.OTHER}>Lainnya</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* Tingkat prioritas */}
          <FormField
            control={control}
            name="priorityLevel"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  <AlertTriangle className="h-4 w-4 text-green-600" />
                  Tingkat Prioritas
                </FormLabel>
                <Select
                  disabled={isPending}
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih tingkat prioritas" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {priorityLevelOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  Tingkat prioritas untuk penanganan servis
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={control}
            name="deviceBrand"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  <HardDrive className="h-4 w-4 text-blue-600" />
                  Merek Perangkat
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="Contoh: Apple, Samsung, HP"
                    {...field}
                    disabled={isPending}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="deviceModel"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  <Laptop className="h-4 w-4 text-blue-600" />
                  Model Perangkat
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="Contoh: iPhone 13, Galaxy S21"
                    {...field}
                    disabled={isPending}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Nomor seri and Estimasi selesai */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={control}
            name="deviceSerialNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  <FileText className="h-4 w-4 text-blue-600" />
                  Nomor Seri (Opsional)
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="Nomor Seri Perangkat"
                    {...field}
                    disabled={isPending}
                  />
                </FormControl>
                <FormDescription>
                  Nomor seri atau IMEI perangkat jika tersedia
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* Estimasi selesai */}
          <FormField
            control={control}
            name="estimatedCompletionDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  Estimasi Selesai
                </FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                        disabled={isPending}
                      >
                        {field.value ? (
                          format(new Date(field.value), "dd MMMM yyyy", {
                            locale: id,
                          })
                        ) : (
                          <span>Pilih tanggal estimasi selesai</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value ? new Date(field.value) : undefined}
                      onSelect={(date) => {
                        field.onChange(
                          date ? date.toISOString().split("T")[0] : ""
                        );
                      }}
                      disabled={(date) => {
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);
                        return date < today;
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormDescription>
                  Perkiraan tanggal selesai perbaikan
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Service Options */}
      <div className="space-y-6">
        {/* <div className="flex items-center gap-2 mb-4">
          <Settings className="h-5 w-5 text-green-600" />
          <h4 className="text-md font-medium text-gray-900">Opsi Servis</h4>
        </div> */}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={control}
            name="warrantyPeriod"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  <Clock className="h-4 w-4 text-green-600" />
                  Masa Garansi (Hari)
                </FormLabel>
                <FormControl>
                  <Input
                    type="text"
                    placeholder="0"
                    {...field}
                    onChange={(e) => {
                      // Only allow numeric input (digits only)
                      const value = e.target.value.replace(/[^0-9]/g, "");
                      // Update the input value with the sanitized value
                      e.target.value = value;
                      // Parse the sanitized value as a number for the form state
                      const numericValue = parseInt(value);
                      field.onChange(isNaN(numericValue) ? 0 : numericValue);
                    }}
                    disabled={isPending}
                  />
                </FormControl>
                <FormDescription>
                  Masa garansi setelah perbaikan selesai
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="estimatedCost"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  <DollarSign className="h-4 w-4 text-red-600" />
                  Estimasi Biaya (Rp)
                </FormLabel>
                <FormControl>
                  <div className="relative flex items-center">
                    <span className="absolute left-0 top-0 bottom-0 flex items-center px-2 text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 rounded-md border border-input m-1 text-sm font-semibold">
                      Rp
                    </span>
                    <Input
                      type="text"
                      placeholder="0"
                      {...field}
                      value={
                        field.value
                          ? new Intl.NumberFormat("id-ID").format(field.value)
                          : ""
                      }
                      onChange={(e) => {
                        const rawValue = e.target.value.replace(/[^0-9]/g, "");
                        const numericValue = parseInt(rawValue);
                        field.onChange(isNaN(numericValue) ? 0 : numericValue);
                      }}
                      disabled={isPending}
                      className="pl-11"
                    />
                  </div>
                </FormControl>
                <FormDescription>
                  Estimasi biaya perbaikan (opsional)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Problem Details */}
        <div className="space-y-6">
          {/* <div className="flex items-center gap-2 mb-4">
          <FileText className="h-5 w-5 text-red-600" />
          <h4 className="text-md font-medium text-gray-900">Detail Masalah</h4>
        </div> */}

          <FormField
            control={control}
            name="problemDescription"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  <FileText className="h-4 w-4 text-red-600" />
                  Deskripsi Masalah
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Jelaskan masalah yang dialami perangkat"
                    className="min-h-32 resize-none"
                    {...field}
                    disabled={isPending}
                  />
                </FormControl>
                <FormDescription>
                  Jelaskan masalah secara detail, termasuk kapan masalah mulai
                  terjadi
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6"></div>
        </div>
      </div>
    </div>
  );
};

export default ServiceInfoSection;
