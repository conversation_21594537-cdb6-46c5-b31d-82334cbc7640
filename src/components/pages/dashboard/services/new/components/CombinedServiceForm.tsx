"use client";

import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { User, Paperclip, Settings } from "lucide-react";
import { ServiceFormValues } from "../types";
import { Control } from "react-hook-form";
import CustomerInfoSection from "./CustomerInfoSection";
import ServiceInfoSection from "./ServiceInfoSection";
import AdditionalInfo from "./AdditionalInfo";
import { Separator } from "@/components/ui/separator";

interface CombinedServiceFormProps {
  control: Control<ServiceFormValues>;
  isPending: boolean;
  setValue?: (name: keyof ServiceFormValues, value: any) => void;
  trigger?: (
    name?: keyof ServiceFormValues | (keyof ServiceFormValues)[]
  ) => Promise<boolean>;
}

const CombinedServiceForm: React.FC<CombinedServiceFormProps> = ({
  control,
  isPending,
  setValue,
  trigger,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Formulir Servis Baru</CardTitle>
        <CardDescription>
          Lengkapi semua informasi untuk membuat servis baru
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* Customer Information Section */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <User className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">Informasi Pelanggan</h3>
          </div>
          <CustomerInfoSection
            control={control}
            isPending={isPending}
            setValue={setValue}
            trigger={trigger}
          />
        </div>

        <Separator />

        {/* Service Information Section (Combined) */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <Settings className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">Informasi Servis</h3>
          </div>
          <ServiceInfoSection control={control} isPending={isPending} />
        </div>

        <Separator />

        {/* Additional Info Section */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <Paperclip className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">Informasi Tambahan</h3>
          </div>
          <AdditionalInfo control={control} isPending={isPending} />
        </div>
      </CardContent>
    </Card>
  );
};

export default CombinedServiceForm;
