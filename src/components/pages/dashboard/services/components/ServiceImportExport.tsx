"use client";

import React, { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
  Download,
  Upload,
  FileSpreadsheet,
  FileText,
  CheckCircle,
  Info,
  Calendar,
  Settings,
} from "lucide-react";
import * as XLSX from "xlsx-js-style";
import { toast } from "sonner";

interface ImportSummary {
  servicesCreated: number;
  errors: string[];
}

interface ExportConfig {
  reportType: "harian" | "bulanan" | "tahunan";
  selectedDate: Date;
  selectedMonth: number;
  selectedYear: number;
  format: "excel" | "csv";
  includeSummary: boolean;
  includeCharts: boolean;
}

interface ServiceImportExportProps {
  onRefresh?: () => void;
}

export const ServiceImportExport: React.FC<ServiceImportExportProps> = ({
  onRefresh,
}) => {
  // Import states
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importSummary, setImportSummary] = useState<ImportSummary | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Export states
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    reportType: "bulanan",
    selectedDate: new Date(),
    selectedMonth: new Date().getMonth(),
    selectedYear: new Date().getFullYear(),
    format: "excel",
    includeSummary: false,
    includeCharts: false,
  });

  // Download template
  const downloadTemplate = () => {
    // Create a simple template for service import
    const templateData = [
      {
        "Nomor Servis": "SRV-25S000001",
        "Nama Pelanggan": "John Doe",
        "Telepon Pelanggan": "081234567890",
        "Email Pelanggan": "<EMAIL>",
        "Alamat Pelanggan": "Jl. Contoh No. 123",
        "Jenis Perangkat": "LAPTOP",
        "Merek Perangkat": "Dell",
        "Model Perangkat": "Inspiron 15",
        "Serial Number": "DL123456789",
        "Deskripsi Masalah": "Laptop tidak bisa menyala",
        "Estimasi Biaya": "500000",
        "Catatan Diagnosis": "Perlu penggantian baterai",
        "Catatan Perbaikan": "Baterai sudah diganti",
        "Periode Garansi": "30",
        "Prioritas": "MEDIUM",
        "Status": "DITERIMA",
      },
    ];

    const worksheet = XLSX.utils.json_to_sheet(templateData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Template Servis");

    const fileName = `template-import-servis-${new Date().toISOString().split("T")[0]}.xlsx`;
    XLSX.writeFile(workbook, fileName);
    toast.success("Template berhasil diunduh");
  };

  // Handle import
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file
    if (!file.name.endsWith(".xlsx") && !file.name.endsWith(".xls")) {
      toast.error("Format file tidak didukung. Gunakan file Excel (.xlsx/.xls)");
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      toast.error("Ukuran file terlalu besar. Maksimal 10MB");
      return;
    }

    setIsImporting(true);
    setImportProgress(0);

    try {
      // Simulate import process
      for (let i = 0; i <= 100; i += 10) {
        setImportProgress(i);
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      // For now, just show a success message
      // In a real implementation, you would process the Excel file here
      setImportSummary({
        servicesCreated: 5,
        errors: [],
      });

      toast.success("Import berhasil!");
      if (onRefresh) onRefresh();
    } catch (error) {
      console.error("Import error:", error);
      toast.error("Gagal mengimpor data servis");
    } finally {
      setIsImporting(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  // Handle export
  const handleExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      // Simulate export process
      for (let i = 0; i <= 100; i += 10) {
        setExportProgress(i);
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      // Create sample export data
      const exportData = [
        {
          "Nomor Servis": "SRV-25S000001",
          "Nama Pelanggan": "John Doe",
          "Telepon": "081234567890",
          "Email": "<EMAIL>",
          "Jenis Perangkat": "LAPTOP",
          "Merek": "Dell",
          "Model": "Inspiron 15",
          "Status": "DITERIMA",
          "Tanggal Masuk": "2025-01-15",
          "Estimasi Biaya": "500000",
        },
      ];

      if (exportConfig.format === "excel") {
        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, "Data Servis");

        const fileName = `data-servis-${exportConfig.reportType}-${new Date().toISOString().split("T")[0]}.xlsx`;
        XLSX.writeFile(workbook, fileName);
      } else {
        // CSV export
        const csvContent = [
          Object.keys(exportData[0]).join(","),
          ...exportData.map((row) => Object.values(row).join(",")),
        ].join("\n");

        const blob = new Blob([csvContent], { type: "text/csv" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `data-servis-${exportConfig.reportType}-${new Date().toISOString().split("T")[0]}.csv`;
        a.click();
        URL.revokeObjectURL(url);
      }

      toast.success("Export berhasil!");
      setShowExportDialog(false);
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Gagal mengekspor data servis");
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="flex gap-2">
      {/* Import Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="gap-2">
            <Upload className="h-4 w-4" />
            Import
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Import Data Servis
            </DialogTitle>
            <DialogDescription>
              Upload file Excel untuk mengimpor data servis secara massal
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {!isImporting && !importSummary && (
              <>
                <div className="space-y-2">
                  <Button
                    variant="outline"
                    onClick={downloadTemplate}
                    className="w-full gap-2"
                  >
                    <FileSpreadsheet className="h-4 w-4" />
                    Download Template
                  </Button>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="file-upload">Pilih File Excel</Label>
                  <input
                    ref={fileInputRef}
                    id="file-upload"
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={handleImport}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  />
                </div>
              </>
            )}

            {isImporting && (
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-sm text-muted-foreground mb-2">
                    Mengimpor data servis...
                  </div>
                  <Progress value={importProgress} className="w-full" />
                  <div className="text-xs text-muted-foreground mt-1">
                    {importProgress}%
                  </div>
                </div>
              </div>
            )}

            {importSummary && (
              <div className="space-y-4">
                <div className="flex items-center gap-2 text-green-600">
                  <CheckCircle className="h-5 w-5" />
                  <span className="font-medium">Import Berhasil!</span>
                </div>
                <div className="space-y-2 text-sm">
                  <div>Servis dibuat: {importSummary.servicesCreated}</div>
                  {importSummary.errors.length > 0 && (
                    <div className="text-red-600">
                      Error: {importSummary.errors.length}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowImportDialog(false);
                setImportSummary(null);
              }}
            >
              Tutup
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Export Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="gap-2">
            <Download className="h-4 w-4" />
            Export
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Export Data Servis
            </DialogTitle>
            <DialogDescription>
              Ekspor data servis ke file Excel atau CSV
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {!isExporting && (
              <>
                <div className="space-y-2">
                  <Label>Periode</Label>
                  <Select
                    value={exportConfig.reportType}
                    onValueChange={(value: "harian" | "bulanan" | "tahunan") =>
                      setExportConfig({ ...exportConfig, reportType: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="harian">Harian</SelectItem>
                      <SelectItem value="bulanan">Bulanan</SelectItem>
                      <SelectItem value="tahunan">Tahunan</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Format</Label>
                  <Select
                    value={exportConfig.format}
                    onValueChange={(value: "excel" | "csv") =>
                      setExportConfig({ ...exportConfig, format: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                      <SelectItem value="csv">CSV (.csv)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}

            {isExporting && (
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-sm text-muted-foreground mb-2">
                    Mengekspor data servis...
                  </div>
                  <Progress value={exportProgress} className="w-full" />
                  <div className="text-xs text-muted-foreground mt-1">
                    {exportProgress}%
                  </div>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowExportDialog(false)}
              disabled={isExporting}
            >
              Batal
            </Button>
            <Button onClick={handleExport} disabled={isExporting}>
              {isExporting ? "Mengekspor..." : "Export"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
