"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Filter, X } from "lucide-react";
import { ServiceStatus, DeviceType } from "../types";

export interface ServiceFilterState {
  status?: ServiceStatus;
  deviceType?: DeviceType;
  customerName?: string;
  serviceNumber?: string;
  dateRange?: {
    from?: Date;
    to?: Date;
  };
  estimatedCostRange?: {
    min?: number;
    max?: number;
  };
  isDraft?: boolean;
}

interface ServiceFilterProps {
  filters: ServiceFilterState;
  onFilterChange: (filters: ServiceFilterState) => void;
}

export const ServiceFilter: React.FC<ServiceFilterProps> = ({
  filters,
  onFilterChange,
}) => {
  const [showFilterDialog, setShowFilterDialog] = useState(false);
  const [tempFilters, setTempFilters] = useState<ServiceFilterState>(filters);

  const handleApplyFilters = () => {
    onFilterChange(tempFilters);
    setShowFilterDialog(false);
  };

  const handleResetFilters = () => {
    const resetFilters: ServiceFilterState = {};
    setTempFilters(resetFilters);
    onFilterChange(resetFilters);
    setShowFilterDialog(false);
  };

  const getActiveFilterCount = () => {
    return Object.values(filters).filter((value) => {
      if (value === undefined || value === null || value === "") return false;
      if (typeof value === "object" && Object.keys(value).length === 0) return false;
      return true;
    }).length;
  };

  const getStatusLabel = (status: ServiceStatus) => {
    switch (status) {
      case ServiceStatus.DITERIMA:
        return "Diterima";
      case ServiceStatus.PROSES_MENUNGGU_SPAREPART:
        return "Proses/Menunggu Sparepart";
      case ServiceStatus.SELESAI_BELUM_DIAMBIL:
        return "Selesai & Belum Diambil";
      case ServiceStatus.SELESAI_SUDAH_DIAMBIL:
        return "Selesai & Sudah Diambil";
      default:
        return status;
    }
  };

  const getDeviceTypeLabel = (deviceType: DeviceType) => {
    switch (deviceType) {
      case DeviceType.LAPTOP:
        return "Laptop";
      case DeviceType.DESKTOP:
        return "Desktop";
      case DeviceType.PHONE:
        return "Handphone";
      case DeviceType.TABLET:
        return "Tablet";
      case DeviceType.PRINTER:
        return "Printer";
      case DeviceType.OTHER:
        return "Lainnya";
      default:
        return deviceType;
    }
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <Dialog open={showFilterDialog} onOpenChange={setShowFilterDialog}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2 relative">
          <Filter className="h-4 w-4" />
          Filter
          {activeFilterCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filter Data Servis
          </DialogTitle>
          <DialogDescription>
            Gunakan filter untuk mempersempit pencarian data servis
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Status Filter */}
          <div className="space-y-2">
            <Label>Status Servis</Label>
            <Select
              value={tempFilters.status || ""}
              onValueChange={(value) =>
                setTempFilters({
                  ...tempFilters,
                  status: value ? (value as ServiceStatus) : undefined,
                })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Semua Status</SelectItem>
                <SelectItem value={ServiceStatus.DITERIMA}>
                  {getStatusLabel(ServiceStatus.DITERIMA)}
                </SelectItem>
                <SelectItem value={ServiceStatus.PROSES_MENUNGGU_SPAREPART}>
                  {getStatusLabel(ServiceStatus.PROSES_MENUNGGU_SPAREPART)}
                </SelectItem>
                <SelectItem value={ServiceStatus.SELESAI_BELUM_DIAMBIL}>
                  {getStatusLabel(ServiceStatus.SELESAI_BELUM_DIAMBIL)}
                </SelectItem>
                <SelectItem value={ServiceStatus.SELESAI_SUDAH_DIAMBIL}>
                  {getStatusLabel(ServiceStatus.SELESAI_SUDAH_DIAMBIL)}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Device Type Filter */}
          <div className="space-y-2">
            <Label>Jenis Perangkat</Label>
            <Select
              value={tempFilters.deviceType || ""}
              onValueChange={(value) =>
                setTempFilters({
                  ...tempFilters,
                  deviceType: value ? (value as DeviceType) : undefined,
                })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih jenis perangkat" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Semua Jenis</SelectItem>
                <SelectItem value={DeviceType.LAPTOP}>
                  {getDeviceTypeLabel(DeviceType.LAPTOP)}
                </SelectItem>
                <SelectItem value={DeviceType.DESKTOP}>
                  {getDeviceTypeLabel(DeviceType.DESKTOP)}
                </SelectItem>
                <SelectItem value={DeviceType.PHONE}>
                  {getDeviceTypeLabel(DeviceType.PHONE)}
                </SelectItem>
                <SelectItem value={DeviceType.TABLET}>
                  {getDeviceTypeLabel(DeviceType.TABLET)}
                </SelectItem>
                <SelectItem value={DeviceType.PRINTER}>
                  {getDeviceTypeLabel(DeviceType.PRINTER)}
                </SelectItem>
                <SelectItem value={DeviceType.OTHER}>
                  {getDeviceTypeLabel(DeviceType.OTHER)}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Customer Name Filter */}
          <div className="space-y-2">
            <Label>Nama Pelanggan</Label>
            <Input
              placeholder="Cari berdasarkan nama pelanggan"
              value={tempFilters.customerName || ""}
              onChange={(e) =>
                setTempFilters({
                  ...tempFilters,
                  customerName: e.target.value || undefined,
                })
              }
            />
          </div>

          {/* Service Number Filter */}
          <div className="space-y-2">
            <Label>Nomor Servis</Label>
            <Input
              placeholder="Cari berdasarkan nomor servis"
              value={tempFilters.serviceNumber || ""}
              onChange={(e) =>
                setTempFilters({
                  ...tempFilters,
                  serviceNumber: e.target.value || undefined,
                })
              }
            />
          </div>

          {/* Estimated Cost Range */}
          <div className="space-y-2">
            <Label>Rentang Estimasi Biaya</Label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                type="number"
                placeholder="Min"
                value={tempFilters.estimatedCostRange?.min || ""}
                onChange={(e) =>
                  setTempFilters({
                    ...tempFilters,
                    estimatedCostRange: {
                      ...tempFilters.estimatedCostRange,
                      min: e.target.value ? Number(e.target.value) : undefined,
                    },
                  })
                }
              />
              <Input
                type="number"
                placeholder="Max"
                value={tempFilters.estimatedCostRange?.max || ""}
                onChange={(e) =>
                  setTempFilters({
                    ...tempFilters,
                    estimatedCostRange: {
                      ...tempFilters.estimatedCostRange,
                      max: e.target.value ? Number(e.target.value) : undefined,
                    },
                  })
                }
              />
            </div>
          </div>

          {/* Draft Filter */}
          <div className="space-y-2">
            <Label>Status Draft</Label>
            <Select
              value={
                tempFilters.isDraft === undefined
                  ? ""
                  : tempFilters.isDraft
                  ? "true"
                  : "false"
              }
              onValueChange={(value) =>
                setTempFilters({
                  ...tempFilters,
                  isDraft:
                    value === "" ? undefined : value === "true" ? true : false,
                })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih status draft" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Semua</SelectItem>
                <SelectItem value="true">Draft</SelectItem>
                <SelectItem value="false">Bukan Draft</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={handleResetFilters}>
            <X className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button onClick={handleApplyFilters}>
            <Filter className="h-4 w-4 mr-2" />
            Terapkan Filter
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
