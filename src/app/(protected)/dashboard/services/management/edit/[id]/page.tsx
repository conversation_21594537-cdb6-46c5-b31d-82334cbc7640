import React from "react";
import { notFound } from "next/navigation";
import ServiceEditPage from "@/components/pages/dashboard/services/edit";
import { getServiceById } from "@/lib/get-services";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";

type Props = {
  params: Promise<{ id: string }>;
};

// This is an async Server Component
export default async function EditService(props: Props) {
  // Get the id from params (which is now a Promise)
  const params = await props.params;
  const id = params.id;

  // Get the effective user ID (owner or employee)
  const effectiveUserId = await getEffectiveUserId();

  // If no user ID, return 404
  if (!effectiveUserId) {
    notFound();
  }

  // Fetch the service with the given ID
  const service = await getServiceById(id, effectiveUserId);

  // If service not found, return 404
  if (!service) {
    notFound();
  }

  return <ServiceEditPage service={service} />;
}
